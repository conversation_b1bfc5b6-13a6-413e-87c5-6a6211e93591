<!--
 * @Description: 属性面板组件
 * @Author: AI Assistant  
 * @Date: 2025-08-28
 * @Features: 上下文相关信息、数据验证结果、历史数据对比
-->
<template>
  <div class="property-panel">
    <!-- 节点信息 -->
    <div class="panel-section" v-if="selectedNode">
      <div class="section-header">
        <h4>节点信息</h4>
      </div>
      <div class="section-content">
        <div class="info-item">
          <label>节点类型：</label>
          <span>{{ getNodeTypeText(selectedNode.type) }}</span>
        </div>
        <div class="info-item">
          <label>节点名称：</label>
          <span>{{ selectedNode.title }}</span>
        </div>
        <div class="info-item" v-if="selectedNode.stats">
          <label>项目数量：</label>
          <span>{{ selectedNode.stats.items }}项</span>
        </div>
        <div class="info-item" v-if="selectedNode.stats">
          <label>金额合计：</label>
          <span>¥{{ selectedNode.stats.amount }}万</span>
        </div>
      </div>
    </div>

    <!-- 数据验证结果 -->
    <div class="panel-section">
      <div class="section-header">
        <h4>数据验证结果</h4>
      </div>
      <div class="section-content">
        <div class="validation-summary">
          <div class="error-count" v-if="validationResult.errors.length > 0">
            <span class="icon">❌</span>
            <span>发现 {{ validationResult.errors.length }} 个错误</span>
          </div>
          <div class="warning-count" v-if="validationResult.warnings.length > 0">
            <span class="icon">⚠️</span>
            <span>发现 {{ validationResult.warnings.length }} 个警告</span>
          </div>
          <div class="success-message" v-if="validationResult.errors.length === 0 && validationResult.warnings.length === 0">
            <span class="icon">✅</span>
            <span>数据验证通过</span>
          </div>
        </div>

        <!-- 错误列表 -->
        <div class="validation-list" v-if="validationResult.errors.length > 0">
          <div 
            v-for="error in validationResult.errors" 
            :key="error.id"
            class="validation-item error-item"
          >
            <div class="item-content">
              <span class="item-text">第{{ error.row }}行：{{ error.message }}</span>
              <a-button type="link" size="small" @click="locateError(error)">
                定位
              </a-button>
            </div>
          </div>
        </div>

        <!-- 警告列表 -->
        <div class="validation-list" v-if="validationResult.warnings.length > 0">
          <div 
            v-for="warning in validationResult.warnings" 
            :key="warning.id"
            class="validation-item warning-item"
          >
            <div class="item-content">
              <span class="item-text">第{{ warning.row }}行：{{ warning.message }}</span>
              <a-button type="link" size="small" @click="locateWarning(warning)">
                查看
              </a-button>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="validation-actions" v-if="validationResult.errors.length > 0 || validationResult.warnings.length > 0">
          <a-button 
            type="primary" 
            size="small" 
            @click="fixAllErrors"
            :disabled="validationResult.errors.length === 0"
          >
            修复全部
          </a-button>
          <a-button 
            size="small" 
            @click="ignoreWarnings"
            :disabled="validationResult.warnings.length === 0"
          >
            忽略警告
          </a-button>
        </div>
      </div>
    </div>

    <!-- 历史数据对比 -->
    <div class="panel-section">
      <div class="section-header">
        <h4>历史数据对比</h4>
      </div>
      <div class="section-content">
        <div class="compare-selector">
          <label>对比项目：</label>
          <a-select 
            v-model:value="selectedCompareProject" 
            placeholder="选择对比项目"
            style="width: 100%"
            size="small"
          >
            <a-select-option 
              v-for="project in compareProjects" 
              :key="project.id" 
              :value="project.id"
            >
              {{ project.name }}
            </a-select-option>
          </a-select>
        </div>

        <div class="compare-results" v-if="selectedCompareProject">
          <div class="compare-title">当前项目 vs 对比项目</div>
          <div 
            v-for="item in compareData" 
            :key="item.category"
            class="compare-item"
          >
            <div class="category-name">{{ item.category }}：</div>
            <div class="compare-values">
              <span class="current-value">{{ item.current }}元</span>
              <span class="vs-text">vs</span>
              <span class="compare-value">{{ item.compare }}元</span>
              <span class="diff-indicator" :class="item.diffType">
                {{ item.diffType === 'up' ? '↑' : '↓' }} {{ item.diffPercent }}%
              </span>
              <a-button type="link" size="small" @click="showDetailCompare(item)">
                📊
              </a-button>
            </div>
          </div>

          <div class="compare-actions">
            <a-button size="small" @click="showDetailedCompare">
              详细对比
            </a-button>
            <a-button size="small" @click="exportCompare">
              导出
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="panel-section">
      <div class="section-header">
        <h4>快速操作</h4>
      </div>
      <div class="section-content">
        <div class="quick-actions">
          <a-button block size="small" @click="refreshData">
            🔄 刷新数据
          </a-button>
          <a-button block size="small" @click="exportData">
            📤 导出数据
          </a-button>
          <a-button block size="small" @click="printData">
            🖨️ 打印
          </a-button>
          <a-button block size="small" @click="showHelp">
            ❓ 帮助
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'

// Props
const props = defineProps({
  selectedNode: {
    type: Object,
    default: null
  },
  contextInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['property-change', 'locate-error', 'locate-warning'])

// 响应式数据
const selectedCompareProject = ref(null)

// 验证结果数据
const validationResult = ref({
  errors: [
    { id: 1, row: 5, message: '工程量不能为0' },
    { id: 2, row: 12, message: '编码格式错误' },
    { id: 3, row: 18, message: '单价异常偏高' }
  ],
  warnings: [
    { id: 1, row: 8, message: '材料价格偏高' },
    { id: 2, row: 15, message: '工程量偏大' }
  ]
})

// 对比项目列表
const compareProjects = ref([
  { id: 1, name: '某类似住宅项目' },
  { id: 2, name: '某办公楼项目' },
  { id: 3, name: '某商业综合体项目' }
])

// 对比数据
const compareData = ref([
  {
    category: '土方工程',
    current: '8.5',
    compare: '7.8',
    diffPercent: '8.9',
    diffType: 'up'
  },
  {
    category: '砌体工程', 
    current: '156',
    compare: '162',
    diffPercent: '3.7',
    diffType: 'down'
  }
])

// 方法定义
const getNodeTypeText = (type) => {
  const typeMap = {
    project: '工程项目',
    unit: '单项工程',
    subunit: '单位工程'
  }
  return typeMap[type] || '未知类型'
}

const locateError = (error) => {
  emit('locate-error', error)
  message.info(`定位到第${error.row}行错误`)
}

const locateWarning = (warning) => {
  emit('locate-warning', warning)
  message.info(`定位到第${warning.row}行警告`)
}

const fixAllErrors = () => {
  message.success('正在修复所有错误...')
  // 模拟修复过程
  setTimeout(() => {
    validationResult.value.errors = []
    message.success('所有错误已修复')
  }, 1000)
}

const ignoreWarnings = () => {
  validationResult.value.warnings = []
  message.success('已忽略所有警告')
}

const showDetailCompare = (item) => {
  message.info(`查看${item.category}详细对比`)
}

const showDetailedCompare = () => {
  message.info('显示详细对比报告')
}

const exportCompare = () => {
  message.success('对比报告已导出')
}

const refreshData = () => {
  message.success('数据已刷新')
}

const exportData = () => {
  message.success('数据已导出')
}

const printData = () => {
  message.info('正在打印...')
}

const showHelp = () => {
  message.info('显示帮助信息')
}

// 监听节点变化
watch(() => props.selectedNode, (newNode) => {
  if (newNode) {
    // 根据节点类型更新验证结果
    console.log('节点变化:', newNode)
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.property-panel {
  height: 100%;
  overflow-y: auto;
}

.panel-section {
  border-bottom: 1px solid #e8e8e8;
  
  &:last-child {
    border-bottom: none;
  }
}

.section-header {
  padding: 8px 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  
  h4 {
    margin: 0;
    font-size: 13px;
    font-weight: 600;
    color: #333;
  }
}

.section-content {
  padding: 12px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 12px;
  
  label {
    min-width: 60px;
    color: #666;
    font-weight: 500;
  }
  
  span {
    color: #333;
  }
}

.validation-summary {
  margin-bottom: 12px;
  
  .error-count,
  .warning-count,
  .success-message {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
    font-size: 12px;
    
    .icon {
      font-size: 14px;
    }
  }
  
  .error-count {
    color: #ff4d4f;
  }
  
  .warning-count {
    color: #faad14;
  }
  
  .success-message {
    color: #52c41a;
  }
}

.validation-list {
  margin-bottom: 12px;
}

.validation-item {
  margin-bottom: 8px;
  padding: 6px;
  border-radius: 4px;
  font-size: 11px;
  
  &.error-item {
    background: #fff2f0;
    border: 1px solid #ffccc7;
  }
  
  &.warning-item {
    background: #fffbe6;
    border: 1px solid #ffe58f;
  }
  
  .item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .item-text {
      flex: 1;
    }
  }
}

.validation-actions {
  display: flex;
  gap: 8px;
}

.compare-selector {
  margin-bottom: 12px;
  
  label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    color: #666;
  }
}

.compare-results {
  .compare-title {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
  }
}

.compare-item {
  margin-bottom: 8px;
  font-size: 11px;
  
  .category-name {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .compare-values {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .current-value {
      color: #1890ff;
    }
    
    .vs-text {
      color: #999;
    }
    
    .compare-value {
      color: #666;
    }
    
    .diff-indicator {
      font-weight: 600;
      
      &.up {
        color: #ff4d4f;
      }
      
      &.down {
        color: #52c41a;
      }
    }
  }
}

.compare-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
