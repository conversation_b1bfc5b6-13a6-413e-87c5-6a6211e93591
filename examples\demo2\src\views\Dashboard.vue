<template>
  <div class="dashboard">
    <a-layout class="layout">
      <!-- 使用共享头部组件 -->
      <AppHeader
        class="dashboard-header"
        title="造价管理系统"
        :show-window-controls="true"
        :is-maximized="isMaximized"
        @minimize="minimizeWindow"
        @maximize="toggleMaximize"
        @close="closeWindow"
      >
        <template #right>
          <div class="user-info">
            <a-space>
              <a-avatar :size="32" style="background-color: rgba(255, 255, 255, 0.2); color: white;">
                <template #icon><UserOutlined /></template>
              </a-avatar>
              <span style="color: white; font-weight: 500;">管理员</span>
            </a-space>
          </div>
        </template>
      </AppHeader>

      <!-- 主内容区 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <!-- 软件标题 -->
          <div class="software-title">
            <h1>河北省工程造价计价软件 V1.0</h1>
          </div>

          <!-- 地区选择区域 -->
          <a-card class="region-selector section-card" title="地区选择区域">
            <div class="region-controls">
              <div class="control-group">
                <label>省份：</label>
                <a-select
                  v-model:value="selectedProvince"
                  placeholder="河北省"
                  style="width: 120px"
                  @change="handleProvinceChange"
                >
                  <a-select-option value="hebei">河北省</a-select-option>
                  <a-select-option value="beijing">北京市</a-select-option>
                  <a-select-option value="tianjin">天津市</a-select-option>
                </a-select>
              </div>
              <div class="control-group">
                <label>地市：</label>
                <a-select
                  v-model:value="selectedCity"
                  placeholder="选择"
                  style="width: 120px"
                  :disabled="!selectedProvince"
                >
                  <a-select-option
                    v-for="city in availableCities"
                    :key="city.value"
                    :value="city.value"
                  >
                    {{ city.label }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </a-card>

          <!-- 业务类型选择 -->
          <a-card class="business-type-selector section-card" title="业务类型选择">
            <div class="business-type-controls">
              <a-radio-group v-model:value="selectedBusinessType" @change="handleBusinessTypeChange">
                <a-radio value="estimate">概算</a-radio>
                <a-radio value="budget" checked>预算</a-radio>
                <a-radio value="settlement">结算</a-radio>
                <a-radio value="audit">审核</a-radio>
              </a-radio-group>
            </div>
          </a-card>

          <!-- 最近项目列表 -->
          <a-card class="recent-projects section-card" title="最近项目列表">
            <div class="projects-list">
              <div
                v-for="project in recentProjects"
                :key="project.id"
                class="project-item"
                @dblclick="openProject(project)"
                @click="selectProject(project)"
                :class="{ 'selected': selectedProject?.id === project.id }"
              >
                <div class="project-icon">📁</div>
                <div class="project-info">
                  <div class="project-name">{{ project.name }}</div>
                  <div class="project-code">{{ project.code }}</div>
                </div>
              </div>
              <div v-if="recentProjects.length === 0" class="no-projects">
                暂无最近项目
              </div>
            </div>
          </a-card>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-button
              type="primary"
              size="large"
              @click="showCreateProjectWizard"
              :disabled="!selectedProvince || !selectedCity"
            >
              {{ getNewButtonText() }}
            </a-button>
            <a-button
              size="large"
              @click="openExistingProject"
            >
              打开项目
            </a-button>
            <a-button
              size="large"
              @click="showSettings"
            >
              设置
            </a-button>
          </div>

          <!-- 子窗口演示 -->
          <!-- 演示区域可以根据需要添加内容 -->

          <!-- 系统状态 -->
          <a-card title="系统状态" class="status-card">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="微前端模块" :value="6" suffix="个" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="已开发模块" :value="1" suffix="个" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="系统状态" value="正常" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="在线用户" :value="1" suffix="人" />
              </a-col>
            </a-row>
          </a-card>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import { AppHeader, useMainWindowManagement } from '@cost-app/shared-components'
import {
  UserOutlined,
  CalculatorOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  AuditOutlined
} from '@ant-design/icons-vue'
// 使用新的简化窗口管理
const {
  isMaximized,
  minimizeWindow: minimize,
  toggleMaximize: toggle,
  closeWindow: close
} = useMainWindowManagement()

// 包装窗口操作函数以添加用户反馈
const minimizeWindow = async () => {
  try {
    await minimize()
    message.success('窗口已最小化')
  } catch (error) {
    message.error('窗口操作失败')
  }
}

const toggleMaximize = async () => {
  try {
    await toggle()
  } catch (error) {
    message.error('窗口操作失败')
  }
}

const closeWindow = async () => {
  try {
    await close()
  } catch (error) {
    message.error('窗口操作失败')
  }
}

// 地区选择相关数据
const selectedProvince = ref('hebei')
const selectedCity = ref('')
const selectedBusinessType = ref('budget')
const selectedProject = ref(null)
const recentProjects = ref([])

// 地市数据映射
const cityMap = {
  hebei: [
    { value: 'shijiazhuang', label: '石家庄市' },
    { value: 'baoding', label: '保定市' },
    { value: 'tangshan', label: '唐山市' },
    { value: 'langfang', label: '廊坊市' },
    { value: 'cangzhou', label: '沧州市' }
  ],
  beijing: [
    { value: 'dongcheng', label: '东城区' },
    { value: 'xicheng', label: '西城区' },
    { value: 'chaoyang', label: '朝阳区' },
    { value: 'haidian', label: '海淀区' }
  ],
  tianjin: [
    { value: 'heping', label: '和平区' },
    { value: 'hexi', label: '河西区' },
    { value: 'hedong', label: '河东区' },
    { value: 'nankai', label: '南开区' }
  ]
}

// 计算属性
const availableCities = computed(() => {
  return cityMap[selectedProvince.value] || []
})

const modules = ref([
  {
    key: 'rough-estimate',
    title: '概算',
    description: '项目概算管理和计算',
    icon: CalculatorOutlined,
    color: '#1890ff',
    status: 'ready',
    port: 5174
  },
  {
    key: 'budget',
    title: '预算',
    description: '项目预算编制和管理',
    icon: DollarOutlined,
    color: '#52c41a',
    status: 'development',
    port: 5175
  },
  {
    key: 'budget-review',
    title: '预算审核',
    description: '预算审核流程管理',
    icon: CheckCircleOutlined,
    color: '#faad14',
    status: 'development',
    port: 5176
  },
  {
    key: 'settlement',
    title: '结算',
    description: '项目结算管理',
    icon: FileTextOutlined,
    color: '#722ed1',
    status: 'development',
    port: 5177
  },
  {
    key: 'settlement-review',
    title: '结算审核',
    description: '结算审核流程管理',
    icon: AuditOutlined,
    color: '#eb2f96',
    status: 'development',
    port: 5178
  },
])

// 地区选择处理
const handleProvinceChange = (value) => {
  selectedCity.value = ''
  // 自动加载对应地市列表
  if (availableCities.value.length > 0) {
    selectedCity.value = availableCities.value[0].value
  }
}

// 业务类型变化处理
const handleBusinessTypeChange = (e) => {
  console.log('业务类型变更为:', e.target.value)
}

// 获取新建按钮文字
const getNewButtonText = () => {
  const typeMap = {
    estimate: '新建概算',
    budget: '新建预算',
    settlement: '新建结算',
    audit: '新建审核'
  }
  return typeMap[selectedBusinessType.value] || '新建项目'
}

// 显示项目创建向导
const showCreateProjectWizard = () => {
  if (!selectedProvince.value || !selectedCity.value) {
    message.warning('请先选择省份和地市')
    return
  }
  // 根据业务类型打开对应的模块
  const moduleMap = {
    estimate: 'rough-estimate',
    budget: 'budget',
    settlement: 'settlement',
    audit: 'settlement-review'
  }
  const moduleKey = moduleMap[selectedBusinessType.value]
  const module = modules.value.find(m => m.key === moduleKey)
  if (module) {
    openModule(module)
  }
}

// 打开现有项目
const openExistingProject = () => {
  message.info('打开项目功能 - Ctrl+O')
}

// 显示设置
const showSettings = () => {
  message.info('设置功能')
}

// 选择项目
const selectProject = (project) => {
  selectedProject.value = project
}

// 打开项目
const openProject = (project) => {
  message.success(`正在打开项目: ${project.name}`)
  // 这里可以根据项目类型打开对应的模块
}

// 加载最近项目
const loadRecentProjects = () => {
  // 显示最近5个项目
  recentProjects.value = [
    {
      id: 1,
      name: '某住宅项目',
      code: 'SJZ2025001',
      lastModified: '2025-08-28'
    },
    {
      id: 2,
      name: '某商业项目',
      code: 'BD2025002',
      lastModified: '2025-08-27'
    }
  ]
}

const openModule = async (module) => {
  try {
    message.loading(`正在打开${module.title}模块...`, 2)

    // 外部模块：使用开发服务器或静态文件
    const isDev = import.meta.env.DEV
    let url

    if (isDev) {
      // 开发环境：使用开发服务器端口
      url = `http://localhost:${module.port}`
    } else {
      // 生产环境：使用相对路径访问打包后的静态文件
      url = `${module.key}/index.html`
    }

    // 调用 Tauri 命令创建新窗口
    await invoke('create_module_window', {
      moduleKey: module.key,
      title: module.title,
      url: url
    })

    message.success(`${module.title}模块已在新窗口中打开`)
  } catch (error) {
    console.error('打开模块失败:', error)
    message.error(`打开${module.title}模块失败: ${error}`)
  }
}

// 快捷键支持
const handleKeydown = (e) => {
  if (e.ctrlKey && e.key === 'n') {
    e.preventDefault()
    showCreateProjectWizard() // Ctrl+N新建项目
  } else if (e.ctrlKey && e.key === 'o') {
    e.preventDefault()
    openExistingProject() // Ctrl+O打开项目
  }
}

// 生命周期
onMounted(() => {
  loadRecentProjects()
  // 默认选择第一个城市
  if (availableCities.value.length > 0) {
    selectedCity.value = availableCities.value[0].value
  }
  document.addEventListener('keydown', handleKeydown)
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 窗口管理通过 useMainWindowManagement 自动初始化
</script>

<style scoped>
.dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  border-bottom: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 0 24px;
}

.header-center {
  flex: 1;
  height: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-header {
  flex-shrink: 0;
  height: 64px;
}

.logo h2 {
  margin: 0;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.content {
  flex: 1;
  background: #f0f2f5;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.software-title {
  text-align: center;
  margin-bottom: 24px;

  h1 {
    font-size: 28px;
    color: #1890ff;
    margin: 0;
    font-weight: 600;
  }
}

.section-card {
  margin-bottom: 16px;
}

.region-controls {
  display: flex;
  gap: 20px;
  align-items: center;

  .control-group {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      font-weight: 500;
      color: #666;
      min-width: 40px;
    }
  }
}

.business-type-controls {
  :deep(.ant-radio-group) {
    display: flex;
    gap: 20px;
  }

  :deep(.ant-radio-wrapper) {
    font-size: 16px;
  }
}

.projects-list {
  max-height: 200px;
  overflow-y: auto;

  .project-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    border: 1px solid transparent;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #e6f7ff;
      border-color: #1890ff;
    }

    .project-icon {
      font-size: 20px;
    }

    .project-info {
      .project-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .project-code {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .no-projects {
    text-align: center;
    color: #999;
    padding: 40px;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 24px;

  .ant-btn {
    min-width: 120px;
    height: 40px;
  }
}

.welcome-card {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.welcome-card :deep(.ant-card-body) {
  padding: 48px 24px;
}

.welcome-content {
  text-align: center;
  color: white;
}

.welcome-content h1 {
  color: white;
  margin-bottom: 8px;
  font-size: 32px;
}

.welcome-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.modules-grid {
  margin-bottom: 24px;
}

.module-card {
  height: 200px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.module-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.module-icon {
  margin-bottom: 16px;
}

.module-content h3 {
  margin: 8px 0;
  color: #262626;
  font-size: 18px;
}

.module-content p {
  color: #8c8c8c;
  margin-bottom: 12px;
  font-size: 14px;
}

.status-card {
  background: white;
}

/* 窗口控制按钮样式 */
.window-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.window-control-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.window-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.window-control-btn.close-btn:hover {
  background-color: #ff4d4f;
  color: white;
}

.window-control-btn {
  color: white;
}

/* 拖动区域样式 */
[data-tauri-drag-region] {
  -webkit-app-region: drag;
}

/* 确保按钮不被拖动 */
.window-controls,
.user-info,
.logo {
  -webkit-app-region: no-drag;
}
</style>
