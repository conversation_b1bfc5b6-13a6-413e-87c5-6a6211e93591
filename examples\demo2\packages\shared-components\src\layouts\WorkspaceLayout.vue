<!--
 * @Description: 工程造价计价软件工作台主界面布局
 * @Author: AI Assistant
 * @Date: 2025-08-28
 * @Features: 结构树、编辑区、属性面板、状态栏
-->
<template>
  <div class="workspace-layout">
    <!-- 系统功能栏 -->
    <header class="system-toolbar">
      <div class="toolbar-left">
        <div class="logo-section">
          <span class="logo">📱</span>
          <span class="app-name">软件Logo</span>
        </div>
        <div class="action-buttons">
          <a-button type="text" size="small" @click="saveProject">
            💾保存
          </a-button>
          <a-button type="text" size="small" @click="undoAction">
            ↶撤销
          </a-button>
        </div>
      </div>
      
      <div class="toolbar-center">
        <div class="project-path">
          📁{{ projectPath }}
        </div>
      </div>
      
      <div class="toolbar-right">
        <div class="user-section">
          <a-dropdown>
            <a-button type="text" size="small">
              👤用户 <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">个人信息</a-menu-item>
                <a-menu-item key="settings">设置</a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">退出登录</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div class="window-controls">
          <a-button type="text" size="small" @click="minimizeWindow">─</a-button>
          <a-button type="text" size="small" @click="toggleMaximize">□</a-button>
          <a-button type="text" size="small" @click="closeWindow">✕</a-button>
        </div>
      </div>
    </header>

    <!-- 项目识别栏 -->
    <div class="project-info-bar">
      <a-tag color="blue">📊{{ projectType }}</a-tag>
      <a-tag color="green">🏗️{{ fileType }}</a-tag>
      <a-tag color="orange">🏢{{ unitType }}</a-tag>
      <a-tag color="purple">📋{{ standardInfo }}</a-tag>
    </div>

    <!-- 主菜单栏 -->
    <nav class="main-menu-bar">
      <a-menu mode="horizontal" :selected-keys="selectedMenuKeys">
        <a-menu-item key="file">📁文件</a-menu-item>
        <a-menu-item key="edit">✏️编制</a-menu-item>
        <a-menu-item key="report">📊报表</a-menu-item>
        <a-menu-item key="electronic">💻电子标</a-menu-item>
        <a-menu-item key="tools">⚙️工具</a-menu-item>
        <a-menu-item key="help">❓帮助</a-menu-item>
      </a-menu>
    </nav>

    <!-- 主工作区 -->
    <div class="main-workspace">
      <!-- 结构树 -->
      <aside class="structure-tree" :style="{ width: treeWidth + 'px' }">
        <div class="tree-header">
          <h4>📁工程项目</h4>
        </div>
        <div class="tree-content">
          <project-structure-tree
            :tree-data="projectStructure"
            :selected-node="selectedNode"
            @node-select="handleNodeSelect"
            @node-rename="handleNodeRename"
            @node-delete="handleNodeDelete"
            @node-add="handleNodeAdd"
          />
        </div>
      </aside>

      <!-- 分隔条 -->
      <div class="resize-handle" @mousedown="startResize"></div>

      <!-- 编辑区 -->
      <main class="edit-area" :style="{ width: `calc(100% - ${treeWidth + propertyWidth + 20}px)` }">
        <div class="edit-tabs">
          <a-tabs v-model:activeKey="activeTabKey" type="card" @change="handleTabChange">
            <a-tab-pane 
              v-for="tab in availableTabs" 
              :key="tab.key" 
              :tab="tab.title"
            >
              <component 
                :is="tab.component" 
                :node-data="selectedNode"
                :project-info="projectInfo"
                @data-change="handleDataChange"
              />
            </a-tab-pane>
          </a-tabs>
        </div>
      </main>

      <!-- 属性面板 -->
      <aside class="property-panel" :style="{ width: propertyWidth + 'px' }">
        <div class="panel-header">
          <h4>属性面板</h4>
        </div>
        <div class="panel-content">
          <property-panel
            :selected-node="selectedNode"
            :context-info="contextInfo"
            @property-change="handlePropertyChange"
          />
        </div>
      </aside>
    </div>

    <!-- 状态栏 -->
    <footer class="status-bar">
      <div class="status-left">
        <span class="status-item">{{ statusText }}</span>
        <span class="status-item">最后保存: {{ lastSaveTime }}</span>
      </div>
      <div class="status-center">
        <span class="status-item">共{{ totalItems }}项</span>
      </div>
      <div class="status-right">
        <span class="status-item">总造价: ¥{{ totalCost }}万</span>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import ProjectStructureTree from '../components/ProjectStructureTree.vue'
import PropertyPanel from '../components/PropertyPanel.vue'

// Props
const props = defineProps({
  projectInfo: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['back-to-console', 'project-save', 'data-change'])

// 响应式数据
const selectedMenuKeys = ref(['file'])
const activeTabKey = ref('overview')
const selectedNode = ref(null)
const treeWidth = ref(250)
const propertyWidth = ref(300)
const isResizing = ref(false)

// 项目信息
const projectPath = computed(() => props.projectInfo?.path || 'C:\\项目\\某住宅小区.gcj')
const projectType = computed(() => props.projectInfo?.type || '预算项目')
const fileType = computed(() => props.projectInfo?.fileType || '招标文件')
const unitType = computed(() => props.projectInfo?.unitType || '单位工程')
const standardInfo = computed(() => props.projectInfo?.standard || '13版清单+22版定额')

// 状态栏信息
const statusText = ref('就绪')
const lastSaveTime = ref('14:30')
const totalItems = ref(1200)
const totalCost = ref('2,850')

// 项目结构数据
const projectStructure = ref([
  {
    key: 'project',
    title: '工程项目名称',
    type: 'project',
    children: [
      {
        key: 'unit1',
        title: '单项1',
        type: 'unit',
        children: [
          { key: 'sub1', title: '单位1', type: 'subunit' }
        ]
      },
      {
        key: 'unit2', 
        title: '单项2',
        type: 'unit',
        children: [
          { key: 'sub2', title: '单位2', type: 'subunit' },
          { key: 'sub3', title: '单位3', type: 'subunit' }
        ]
      }
    ]
  }
])

// 可用的编辑页签
const availableTabs = computed(() => {
  if (!selectedNode.value) return []
  
  const nodeType = selectedNode.value.type
  const baseTabs = [
    { key: 'overview', title: '工程概况', component: 'ProjectOverview' },
    { key: 'analysis', title: '造价分析', component: 'CostAnalysis' },
    { key: 'fee', title: '取费表', component: 'FeeTable' },
    { key: 'summary', title: '人材机汇总', component: 'MaterialSummary' }
  ]
  
  if (nodeType === 'subunit') {
    baseTabs.push(
      { key: 'items', title: '分部分项', component: 'SubItemsEdit' },
      { key: 'measures', title: '措施项目', component: 'MeasuresEdit' },
      { key: 'others', title: '其他项目', component: 'OthersEdit' },
      { key: 'cost-summary', title: '费用汇总', component: 'CostSummary' }
    )
  }
  
  return baseTabs
})

// 上下文信息
const contextInfo = computed(() => ({
  nodeType: selectedNode.value?.type,
  nodeData: selectedNode.value,
  projectInfo: props.projectInfo
}))

// 方法定义
const saveProject = () => {
  message.success('项目已保存')
  lastSaveTime.value = new Date().toLocaleTimeString('zh-CN', { hour12: false }).slice(0, 5)
  emit('project-save')
}

const undoAction = () => {
  message.info('撤销操作')
}

const minimizeWindow = () => {
  message.info('最小化窗口')
}

const toggleMaximize = () => {
  message.info('切换最大化')
}

const closeWindow = () => {
  emit('back-to-console')
}

const handleNodeSelect = (node) => {
  selectedNode.value = node
  // 根据节点类型设置默认页签
  if (node.type === 'subunit') {
    activeTabKey.value = 'items'
  } else {
    activeTabKey.value = 'overview'
  }
}

const handleNodeRename = (node, newName) => {
  node.title = newName
  message.success('重命名成功')
}

const handleNodeDelete = (node) => {
  message.success('删除成功')
}

const handleNodeAdd = (parentNode, nodeType) => {
  message.success('添加成功')
}

const handleTabChange = (key) => {
  console.log('切换到页签:', key)
}

const handleDataChange = (data) => {
  emit('data-change', data)
}

const handlePropertyChange = (property, value) => {
  console.log('属性变更:', property, value)
}

// 拖拽调整宽度
const startResize = (e) => {
  isResizing.value = true
  const startX = e.clientX
  const startWidth = treeWidth.value
  
  const handleMouseMove = (e) => {
    if (isResizing.value) {
      const deltaX = e.clientX - startX
      treeWidth.value = Math.max(200, Math.min(400, startWidth + deltaX))
    }
  }
  
  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 生命周期
onMounted(() => {
  // 默认选中第一个节点
  if (projectStructure.value.length > 0) {
    selectedNode.value = projectStructure.value[0]
  }
})
</script>

<style lang="scss" scoped>
.workspace-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.system-toolbar {
  height: 40px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .logo-section {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .logo {
        font-size: 16px;
      }
      
      .app-name {
        font-weight: 500;
      }
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .toolbar-center {
    flex: 1;
    text-align: center;
    
    .project-path {
      font-size: 14px;
      color: #666;
    }
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .window-controls {
      display: flex;
      gap: 4px;
    }
  }
}

.project-info-bar {
  height: 32px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 8px;
}

.main-menu-bar {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  
  :deep(.ant-menu) {
    border-bottom: none;
  }
}

.main-workspace {
  flex: 1;
  display: flex;
  min-height: 0;
}

.structure-tree {
  background: #f8fbff;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  
  .tree-header {
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
    
    h4 {
      margin: 0;
      font-size: 14px;
    }
  }
  
  .tree-content {
    flex: 1;
    overflow: auto;
    padding: 8px;
  }
}

.resize-handle {
  width: 4px;
  background: #e8e8e8;
  cursor: col-resize;
  
  &:hover {
    background: #1890ff;
  }
}

.edit-area {
  background: #fff;
  display: flex;
  flex-direction: column;
  
  .edit-tabs {
    flex: 1;
    
    :deep(.ant-tabs-content-holder) {
      height: 100%;
    }
    
    :deep(.ant-tabs-tabpane) {
      height: 100%;
      overflow: auto;
    }
  }
}

.property-panel {
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  
  .panel-header {
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
    
    h4 {
      margin: 0;
      font-size: 14px;
    }
  }
  
  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 8px;
  }
}

.status-bar {
  height: 24px;
  background: #d9e1ef;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 12px;
  
  .status-left,
  .status-center,
  .status-right {
    display: flex;
    gap: 16px;
  }
  
  .status-center {
    flex: 1;
    justify-content: center;
  }
  
  .status-item {
    color: #666;
  }
}
</style>
