<!--
 * @Description: 分部分项编辑组件
 * @Author: AI Assistant
 * @Date: 2025-08-28
 * @Features: 表格编辑、标准库选择器、工程量计算器、明细区交互
-->
<template>
  <div class="sub-items-edit">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <a-button type="primary" size="small" @click="addNewItem">
          ➕ 新增
        </a-button>
        <a-button size="small" @click="deleteSelected" :disabled="!hasSelection">
          🗑️ 删除
        </a-button>
        <a-button size="small" @click="insertFromLibrary">
          📚 插入清单
        </a-button>
        <a-button size="small" @click="insertQuota">
          📋 插入定额
        </a-button>
      </div>
      <div class="toolbar-right">
        <a-button size="small" @click="calculateAll">
          🧮 重新计算
        </a-button>
        <a-button size="small" @click="validateData">
          ✅ 数据验证
        </a-button>
        <a-button size="small" @click="exportData">
          📤 导出
        </a-button>
      </div>
    </div>

    <!-- 主表格 -->
    <div class="main-table">
      <a-table
        :columns="tableColumns"
        :data-source="tableData"
        :row-selection="rowSelection"
        :scroll="{ x: 1500, y: 400 }"
        size="small"
        bordered
        :pagination="false"
        @change="handleTableChange"
      >
        <!-- 项目编码列 -->
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'code'">
            <a-input
              v-model:value="record.code"
              size="small"
              @blur="handleCodeBlur(record, index)"
              @press-enter="handleCodeEnter(record, index)"
              placeholder="点击选择编码"
              readonly
              @click="showCodeSelector(record, index)"
            />
          </template>
          
          <!-- 项目名称列 -->
          <template v-else-if="column.key === 'name'">
            <a-input
              v-model:value="record.name"
              size="small"
              @blur="handleNameChange(record, index)"
              placeholder="项目名称"
            />
          </template>
          
          <!-- 工程量列 -->
          <template v-else-if="column.key === 'quantity'">
            <a-input
              v-model:value="record.quantity"
              size="small"
              @blur="handleQuantityChange(record, index)"
              @click="showQuantityCalculator(record, index)"
              placeholder="0"
              style="text-align: right"
            />
          </template>
          
          <!-- 单价列 -->
          <template v-else-if="column.key === 'unitPrice'">
            <a-input
              v-model:value="record.unitPrice"
              size="small"
              @blur="handleUnitPriceChange(record, index)"
              placeholder="0.00"
              style="text-align: right"
              readonly
            />
          </template>
          
          <!-- 合价列 -->
          <template v-else-if="column.key === 'totalPrice'">
            <span class="total-price">{{ formatCurrency(record.totalPrice) }}</span>
          </template>
          
          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a-space size="small">
              <a-button type="link" size="small" @click="showItemDetail(record, index)">
                📋
              </a-button>
              <a-button type="link" size="small" @click="copyItem(record, index)">
                📄
              </a-button>
              <a-button type="link" size="small" @click="deleteItem(record, index)">
                🗑️
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 明细区 -->
    <div class="detail-area" v-if="selectedItem">
      <a-tabs v-model:activeKey="detailActiveKey" size="small">
        <a-tab-pane key="materials" tab="人材机明细">
          <material-detail-table
            :item-data="selectedItem"
            @material-change="handleMaterialChange"
          />
        </a-tab-pane>
        <a-tab-pane key="price-analysis" tab="单价构成分析">
          <price-analysis-table
            :item-data="selectedItem"
            @price-change="handlePriceChange"
          />
        </a-tab-pane>
        <a-tab-pane key="quantity-calc" tab="工程量计算">
          <quantity-calculator
            :item-data="selectedItem"
            @quantity-change="handleQuantityCalculatorChange"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 编码选择器 -->
    <code-selector-modal
      v-model:visible="codeSelectorVisible"
      :current-record="currentEditRecord"
      @code-selected="handleCodeSelected"
    />

    <!-- 工程量计算器 -->
    <quantity-calculator-modal
      v-model:visible="quantityCalculatorVisible"
      :current-record="currentEditRecord"
      @quantity-calculated="handleQuantityCalculated"
    />

    <!-- 项目详情 -->
    <item-detail-modal
      v-model:visible="itemDetailVisible"
      :item-data="currentEditRecord"
      @item-updated="handleItemUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import MaterialDetailTable from './MaterialDetailTable.vue'
import PriceAnalysisTable from './PriceAnalysisTable.vue'
import QuantityCalculator from './QuantityCalculator.vue'
import CodeSelectorModal from './CodeSelectorModal.vue'
import QuantityCalculatorModal from './QuantityCalculatorModal.vue'
import ItemDetailModal from './ItemDetailModal.vue'

// Props
const props = defineProps({
  nodeData: {
    type: Object,
    default: null
  },
  projectInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['data-change'])

// 响应式数据
const selectedRowKeys = ref([])
const selectedItem = ref(null)
const detailActiveKey = ref('materials')
const codeSelectorVisible = ref(false)
const quantityCalculatorVisible = ref(false)
const itemDetailVisible = ref(false)
const currentEditRecord = ref(null)
const currentEditIndex = ref(-1)

// 表格列定义
const tableColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '项目编码',
    key: 'code',
    width: 120,
    ellipsis: true
  },
  {
    title: '项目名称',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '计量单位',
    key: 'unit',
    width: 80,
    align: 'center'
  },
  {
    title: '工程量',
    key: 'quantity',
    width: 100,
    align: 'right'
  },
  {
    title: '综合单价(元)',
    key: 'unitPrice',
    width: 120,
    align: 'right'
  },
  {
    title: '合价(元)',
    key: 'totalPrice',
    width: 120,
    align: 'right'
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
    fixed: 'right'
  }
]

// 表格数据
const tableData = ref([
  {
    id: 1,
    code: '010101001001',
    name: '平整场地',
    unit: 'm²',
    quantity: '1200.00',
    unitPrice: '3.50',
    totalPrice: 4200.00,
    materials: []
  },
  {
    id: 2,
    code: '010102001001',
    name: '挖基础土方',
    unit: 'm³',
    quantity: '850.00',
    unitPrice: '28.50',
    totalPrice: 24225.00,
    materials: []
  }
])

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys, rows) => {
    selectedRowKeys.value = keys
    if (rows.length > 0) {
      selectedItem.value = rows[0]
    } else {
      selectedItem.value = null
    }
  },
  onSelect: (record, selected, selectedRows) => {
    if (selected) {
      selectedItem.value = record
    }
  }
}

// 计算属性
const hasSelection = computed(() => selectedRowKeys.value.length > 0)

// 方法定义
const addNewItem = () => {
  const newItem = {
    id: Date.now(),
    code: '',
    name: '',
    unit: '',
    quantity: '0.00',
    unitPrice: '0.00',
    totalPrice: 0.00,
    materials: []
  }
  tableData.value.push(newItem)
  message.success('已添加新项目')
}

const deleteSelected = () => {
  if (!hasSelection.value) return
  
  tableData.value = tableData.value.filter(item => 
    !selectedRowKeys.value.includes(item.id)
  )
  selectedRowKeys.value = []
  selectedItem.value = null
  message.success('已删除选中项目')
}

const insertFromLibrary = () => {
  message.info('打开清单库选择器')
}

const insertQuota = () => {
  message.info('打开定额库选择器')
}

const calculateAll = () => {
  tableData.value.forEach(item => {
    const quantity = parseFloat(item.quantity) || 0
    const unitPrice = parseFloat(item.unitPrice) || 0
    item.totalPrice = quantity * unitPrice
  })
  message.success('重新计算完成')
}

const validateData = () => {
  message.success('数据验证通过')
}

const exportData = () => {
  message.success('数据已导出')
}

const showCodeSelector = (record, index) => {
  currentEditRecord.value = record
  currentEditIndex.value = index
  codeSelectorVisible.value = true
}

const showQuantityCalculator = (record, index) => {
  currentEditRecord.value = record
  currentEditIndex.value = index
  quantityCalculatorVisible.value = true
}

const showItemDetail = (record, index) => {
  currentEditRecord.value = record
  currentEditIndex.value = index
  itemDetailVisible.value = true
}

const copyItem = (record, index) => {
  const newItem = { ...record, id: Date.now() }
  tableData.value.splice(index + 1, 0, newItem)
  message.success('项目已复制')
}

const deleteItem = (record, index) => {
  tableData.value.splice(index, 1)
  message.success('项目已删除')
}

const handleCodeBlur = (record, index) => {
  // 编码失焦处理
}

const handleCodeEnter = (record, index) => {
  // 编码回车处理
}

const handleNameChange = (record, index) => {
  // 名称变更处理
}

const handleQuantityChange = (record, index) => {
  const quantity = parseFloat(record.quantity) || 0
  const unitPrice = parseFloat(record.unitPrice) || 0
  record.totalPrice = quantity * unitPrice
}

const handleUnitPriceChange = (record, index) => {
  const quantity = parseFloat(record.quantity) || 0
  const unitPrice = parseFloat(record.unitPrice) || 0
  record.totalPrice = quantity * unitPrice
}

const handleTableChange = (pagination, filters, sorter) => {
  // 表格变化处理
}

const handleCodeSelected = (code, itemData) => {
  if (currentEditRecord.value) {
    currentEditRecord.value.code = code
    currentEditRecord.value.name = itemData.name
    currentEditRecord.value.unit = itemData.unit
    currentEditRecord.value.unitPrice = itemData.unitPrice
  }
  codeSelectorVisible.value = false
}

const handleQuantityCalculated = (quantity) => {
  if (currentEditRecord.value) {
    currentEditRecord.value.quantity = quantity.toString()
    handleQuantityChange(currentEditRecord.value, currentEditIndex.value)
  }
  quantityCalculatorVisible.value = false
}

const handleItemUpdated = (itemData) => {
  if (currentEditRecord.value) {
    Object.assign(currentEditRecord.value, itemData)
  }
  itemDetailVisible.value = false
}

const handleMaterialChange = (materials) => {
  if (selectedItem.value) {
    selectedItem.value.materials = materials
  }
}

const handlePriceChange = (priceData) => {
  if (selectedItem.value) {
    selectedItem.value.unitPrice = priceData.unitPrice
    handleUnitPriceChange(selectedItem.value, -1)
  }
}

const handleQuantityCalculatorChange = (quantity) => {
  if (selectedItem.value) {
    selectedItem.value.quantity = quantity.toString()
    handleQuantityChange(selectedItem.value, -1)
  }
}

const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value || 0)
}

// 生命周期
onMounted(() => {
  // 初始化数据
  calculateAll()
})
</script>

<style lang="scss" scoped>
.sub-items-edit {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  
  .toolbar-left,
  .toolbar-right {
    display: flex;
    gap: 8px;
  }
}

.main-table {
  flex: 1;
  min-height: 0;
  
  :deep(.ant-table-tbody > tr > td) {
    padding: 4px 8px;
  }
  
  :deep(.ant-input) {
    border: none;
    padding: 2px 4px;
    
    &:focus {
      border: 1px solid #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .total-price {
    font-weight: 600;
    color: #1890ff;
  }
}

.detail-area {
  height: 200px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
  
  :deep(.ant-tabs-content-holder) {
    height: calc(100% - 40px);
    overflow: auto;
  }
}
</style>
