<!--
 * @Description: 工程造价计价软件启动界面交互组件
 * @Author: AI Assistant
 * @Date: 2025-08-28
 * @Features: 地区选择、业务类型选择、最近项目列表、快捷操作
-->
<template>
  <div class="startup-interface">
    <!-- 主界面容器 (1200×800像素) -->
    <div class="startup-container">
      <!-- 软件标题 -->
      <div class="software-title">
        <h1>河北省工程造价计价软件 V1.0</h1>
      </div>

      <!-- 地区选择区域 -->
      <div class="region-selector section-card">
        <h3>地区选择区域</h3>
        <div class="region-controls">
          <div class="control-group">
            <label>省份：</label>
            <a-select 
              v-model:value="selectedProvince" 
              placeholder="河北省"
              style="width: 120px"
              @change="handleProvinceChange"
            >
              <a-select-option value="hebei">河北省</a-select-option>
              <a-select-option value="beijing">北京市</a-select-option>
              <a-select-option value="tianjin">天津市</a-select-option>
            </a-select>
          </div>
          <div class="control-group">
            <label>地市：</label>
            <a-select 
              v-model:value="selectedCity" 
              placeholder="选择"
              style="width: 120px"
              :disabled="!selectedProvince"
            >
              <a-select-option 
                v-for="city in availableCities" 
                :key="city.value" 
                :value="city.value"
              >
                {{ city.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>

      <!-- 业务类型选择 -->
      <div class="business-type-selector section-card">
        <h3>业务类型选择</h3>
        <div class="business-type-controls">
          <a-radio-group v-model:value="selectedBusinessType" @change="handleBusinessTypeChange">
            <a-radio value="estimate">概算</a-radio>
            <a-radio value="budget" checked>预算</a-radio>
            <a-radio value="settlement">结算</a-radio>
            <a-radio value="audit">审核</a-radio>
          </a-radio-group>
        </div>
      </div>

      <!-- 最近项目列表 -->
      <div class="recent-projects section-card">
        <h3>最近项目列表</h3>
        <div class="projects-list">
          <div 
            v-for="project in recentProjects" 
            :key="project.id"
            class="project-item"
            @dblclick="openProject(project)"
            @click="selectProject(project)"
            :class="{ 'selected': selectedProject?.id === project.id }"
          >
            <div class="project-icon">📁</div>
            <div class="project-info">
              <div class="project-name">{{ project.name }}</div>
              <div class="project-code">{{ project.code }}</div>
            </div>
          </div>
          <div v-if="recentProjects.length === 0" class="no-projects">
            暂无最近项目
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button 
          type="primary" 
          size="large"
          @click="showCreateProjectWizard"
          :disabled="!selectedProvince || !selectedCity"
        >
          {{ getNewButtonText() }}
        </a-button>
        <a-button 
          size="large"
          @click="openExistingProject"
        >
          打开项目
        </a-button>
        <a-button 
          size="large"
          @click="showSettings"
        >
          设置
        </a-button>
      </div>
    </div>

    <!-- 项目创建向导 -->
    <project-creation-wizard
      v-model:visible="wizardVisible"
      :business-type="selectedBusinessType"
      :region="{ province: selectedProvince, city: selectedCity }"
      @project-created="handleProjectCreated"
      @cancel="wizardVisible = false"
    />

    <!-- 设置对话框 -->
    <settings-dialog
      v-model:visible="settingsVisible"
      @close="settingsVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { message } from 'ant-design-vue'
import ProjectCreationWizard from './ProjectCreationWizard.vue'
import SettingsDialog from './SettingsDialog.vue'

// 组件事件定义
const emit = defineEmits(['project-created', 'project-opened'])

// 响应式数据
const selectedProvince = ref('hebei')
const selectedCity = ref('')
const selectedBusinessType = ref('budget')
const selectedProject = ref(null)
const recentProjects = ref([])
const wizardVisible = ref(false)
const settingsVisible = ref(false)

// 地市数据映射
const cityMap = {
  hebei: [
    { value: 'shijiazhuang', label: '石家庄市' },
    { value: 'baoding', label: '保定市' },
    { value: 'tangshan', label: '唐山市' },
    { value: 'langfang', label: '廊坊市' },
    { value: 'cangzhou', label: '沧州市' }
  ],
  beijing: [
    { value: 'dongcheng', label: '东城区' },
    { value: 'xicheng', label: '西城区' },
    { value: 'chaoyang', label: '朝阳区' },
    { value: 'haidian', label: '海淀区' }
  ],
  tianjin: [
    { value: 'heping', label: '和平区' },
    { value: 'hexi', label: '河西区' },
    { value: 'hedong', label: '河东区' },
    { value: 'nankai', label: '南开区' }
  ]
}

// 计算属性
const availableCities = computed(() => {
  return cityMap[selectedProvince.value] || []
})

// 方法定义
const handleProvinceChange = (value) => {
  selectedCity.value = ''
  // 自动加载对应地市列表
  if (availableCities.value.length > 0) {
    selectedCity.value = availableCities.value[0].value
  }
}

const handleBusinessTypeChange = (e) => {
  // 业务类型影响新建按钮文字
  console.log('业务类型变更为:', e.target.value)
}

const getNewButtonText = () => {
  const typeMap = {
    estimate: '新建概算',
    budget: '新建预算', 
    settlement: '新建结算',
    audit: '新建审核'
  }
  return typeMap[selectedBusinessType.value] || '新建项目'
}

const showCreateProjectWizard = () => {
  if (!selectedProvince.value || !selectedCity.value) {
    message.warning('请先选择省份和地市')
    return
  }
  wizardVisible.value = true
}

const openExistingProject = () => {
  // 打开文件选择对话框 - Ctrl+O快捷键
  message.info('打开项目功能')
}

const showSettings = () => {
  settingsVisible.value = true
}

const selectProject = (project) => {
  selectedProject.value = project
}

const openProject = (project) => {
  // 双击直接打开项目
  emit('project-opened', project)
}

const handleProjectCreated = (projectData) => {
  wizardVisible.value = false
  emit('project-created', projectData)
  // 刷新最近项目列表
  loadRecentProjects()
}

const loadRecentProjects = () => {
  // 显示最近5个项目
  recentProjects.value = [
    {
      id: 1,
      name: '某住宅项目',
      code: 'SJZ2025001',
      lastModified: '2025-08-28'
    },
    {
      id: 2,
      name: '某商业项目', 
      code: '*********',
      lastModified: '2025-08-27'
    }
  ]
}

// 快捷键支持
const handleKeydown = (e) => {
  if (e.ctrlKey && e.key === 'n') {
    e.preventDefault()
    showCreateProjectWizard() // Ctrl+N新建项目
  } else if (e.ctrlKey && e.key === 'o') {
    e.preventDefault()
    openExistingProject() // Ctrl+O打开项目
  }
}

// 生命周期
onMounted(() => {
  loadRecentProjects()
  // 默认选择第一个城市
  if (availableCities.value.length > 0) {
    selectedCity.value = availableCities.value[0].value
  }
  document.addEventListener('keydown', handleKeydown)
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
.startup-interface {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.startup-container {
  width: 1200px;
  height: 800px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.software-title {
  text-align: center;
  
  h1 {
    font-size: 28px;
    color: #1890ff;
    margin: 0;
    font-weight: 600;
  }
}

.section-card {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 20px;
  
  h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
    font-weight: 600;
  }
}

.region-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  
  .control-group {
    display: flex;
    align-items: center;
    gap: 8px;
    
    label {
      font-weight: 500;
      color: #666;
    }
  }
}

.business-type-controls {
  :deep(.ant-radio-group) {
    display: flex;
    gap: 20px;
  }
  
  :deep(.ant-radio-wrapper) {
    font-size: 16px;
  }
}

.projects-list {
  max-height: 200px;
  overflow-y: auto;
  
  .project-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.selected {
      background-color: #e6f7ff;
      border: 1px solid #1890ff;
    }
    
    .project-icon {
      font-size: 20px;
    }
    
    .project-info {
      .project-name {
        font-weight: 500;
        color: #333;
      }
      
      .project-code {
        font-size: 12px;
        color: #999;
      }
    }
  }
  
  .no-projects {
    text-align: center;
    color: #999;
    padding: 40px;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: auto;
  
  .ant-btn {
    min-width: 120px;
    height: 40px;
  }
}
</style>
