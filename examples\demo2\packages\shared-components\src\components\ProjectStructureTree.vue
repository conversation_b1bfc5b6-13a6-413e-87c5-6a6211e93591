<!--
 * @Description: 工程项目结构树组件
 * @Author: AI Assistant
 * @Date: 2025-08-28
 * @Features: 节点操作、状态显示、数据统计、拖拽排序
-->
<template>
  <div class="project-structure-tree">
    <a-tree
      :tree-data="treeData"
      :selected-keys="selectedKeys"
      :expanded-keys="expandedKeys"
      show-icon
      draggable
      @select="handleSelect"
      @expand="handleExpand"
      @drop="handleDrop"
      @right-click="handleRightClick"
    >
      <template #icon="{ dataRef }">
        <span class="node-icon">{{ getNodeIcon(dataRef) }}</span>
      </template>
      
      <template #title="{ dataRef }">
        <div 
          class="tree-node-title"
          :class="getNodeClass(dataRef)"
          @dblclick="handleDoubleClick(dataRef)"
        >
          <span class="node-name">{{ dataRef.title }}</span>
          <span class="node-stats" v-if="dataRef.stats">
            ({{ dataRef.stats.items }}项, ¥{{ dataRef.stats.amount }}万)
          </span>
          <span class="node-status">{{ getNodeStatusIcon(dataRef) }}</span>
        </div>
      </template>
    </a-tree>

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:visible="contextMenuVisible"
      :trigger="['contextmenu']"
      placement="bottomLeft"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleContextMenuClick">
          <a-menu-item key="add-child" :disabled="!canAddChild">
            <PlusOutlined /> 新增下级
          </a-menu-item>
          <a-menu-item key="rename" :disabled="!canRename">
            <EditOutlined /> 重命名
          </a-menu-item>
          <a-menu-item key="delete" :disabled="!canDelete">
            <DeleteOutlined /> 删除节点
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="copy">
            <CopyOutlined /> 复制
          </a-menu-item>
          <a-menu-item key="paste" :disabled="!canPaste">
            <PasteOutlined /> 粘贴
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="properties">
            <SettingOutlined /> 属性设置
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <!-- 重命名输入框 -->
    <a-modal
      v-model:visible="renameModalVisible"
      title="重命名"
      @ok="confirmRename"
      @cancel="cancelRename"
    >
      <a-input
        v-model:value="renameValue"
        placeholder="请输入新名称"
        @press-enter="confirmRename"
        ref="renameInput"
      />
    </a-modal>

    <!-- 新增节点对话框 -->
    <a-modal
      v-model:visible="addNodeModalVisible"
      title="新增节点"
      @ok="confirmAddNode"
      @cancel="cancelAddNode"
    >
      <a-form layout="vertical">
        <a-form-item label="节点类型">
          <a-select v-model:value="newNodeType" placeholder="选择节点类型">
            <a-select-option value="unit">单项工程</a-select-option>
            <a-select-option value="subunit">单位工程</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="节点名称">
          <a-input
            v-model:value="newNodeName"
            placeholder="请输入节点名称"
            @press-enter="confirmAddNode"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  PasteOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// Props
const props = defineProps({
  treeData: {
    type: Array,
    required: true
  },
  selectedNode: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits([
  'node-select',
  'node-rename', 
  'node-delete',
  'node-add',
  'node-copy',
  'node-paste',
  'node-properties'
])

// 响应式数据
const selectedKeys = ref([])
const expandedKeys = ref(['project'])
const contextMenuVisible = ref(false)
const contextMenuNode = ref(null)
const renameModalVisible = ref(false)
const renameValue = ref('')
const renameNode = ref(null)
const addNodeModalVisible = ref(false)
const newNodeType = ref('unit')
const newNodeName = ref('')
const addParentNode = ref(null)
const copiedNode = ref(null)
const renameInput = ref(null)

// 计算属性
const canAddChild = computed(() => {
  if (!contextMenuNode.value) return false
  const nodeType = contextMenuNode.value.type
  return nodeType === 'project' || nodeType === 'unit'
})

const canRename = computed(() => {
  return contextMenuNode.value && contextMenuNode.value.type !== 'project'
})

const canDelete = computed(() => {
  if (!contextMenuNode.value) return false
  const nodeType = contextMenuNode.value.type
  return nodeType !== 'project' && nodeType !== 'subunit'
})

const canPaste = computed(() => {
  return copiedNode.value !== null && canAddChild.value
})

// 监听选中节点变化
watch(() => props.selectedNode, (newNode) => {
  if (newNode) {
    selectedKeys.value = [newNode.key]
  }
}, { immediate: true })

// 方法定义
const getNodeIcon = (node) => {
  const iconMap = {
    project: '📁',
    unit: '📂', 
    subunit: '📄'
  }
  return iconMap[node.type] || '📄'
}

const getNodeClass = (node) => {
  const classes = ['tree-node']
  
  if (node.status === 'editing') classes.push('node-editing')
  if (node.status === 'locked') classes.push('node-locked')
  if (node.status === 'error') classes.push('node-error')
  if (node.status === 'completed') classes.push('node-completed')
  
  return classes
}

const getNodeStatusIcon = (node) => {
  const statusMap = {
    editing: '🔓',
    locked: '🔒',
    error: '❌',
    completed: '✅'
  }
  return statusMap[node.status] || ''
}

const handleSelect = (selectedKeys, { node }) => {
  if (selectedKeys.length > 0) {
    emit('node-select', node.dataRef)
  }
}

const handleExpand = (expandedKeys) => {
  expandedKeys.value = expandedKeys
}

const handleDrop = (info) => {
  const dropKey = info.node.key
  const dragKey = info.dragNode.key
  const dropPos = info.node.pos.split('-')
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])
  
  // 实现拖拽排序逻辑
  message.success('节点移动成功')
}

const handleRightClick = ({ event, node }) => {
  event.preventDefault()
  contextMenuNode.value = node.dataRef
  contextMenuVisible.value = true
}

const handleDoubleClick = (node) => {
  if (canRename.value) {
    startRename(node)
  }
}

const handleContextMenuClick = ({ key }) => {
  contextMenuVisible.value = false
  
  switch (key) {
    case 'add-child':
      startAddNode()
      break
    case 'rename':
      startRename(contextMenuNode.value)
      break
    case 'delete':
      confirmDelete()
      break
    case 'copy':
      copyNode()
      break
    case 'paste':
      pasteNode()
      break
    case 'properties':
      showProperties()
      break
  }
}

const startRename = (node) => {
  renameNode.value = node
  renameValue.value = node.title
  renameModalVisible.value = true
  
  nextTick(() => {
    renameInput.value?.focus()
  })
}

const confirmRename = () => {
  if (!renameValue.value.trim()) {
    message.warning('请输入有效的名称')
    return
  }
  
  emit('node-rename', renameNode.value, renameValue.value.trim())
  renameModalVisible.value = false
  renameNode.value = null
  renameValue.value = ''
}

const cancelRename = () => {
  renameModalVisible.value = false
  renameNode.value = null
  renameValue.value = ''
}

const startAddNode = () => {
  addParentNode.value = contextMenuNode.value
  newNodeType.value = 'unit'
  newNodeName.value = ''
  addNodeModalVisible.value = true
}

const confirmAddNode = () => {
  if (!newNodeName.value.trim()) {
    message.warning('请输入节点名称')
    return
  }
  
  emit('node-add', addParentNode.value, {
    type: newNodeType.value,
    name: newNodeName.value.trim()
  })
  
  addNodeModalVisible.value = false
  addParentNode.value = null
  newNodeType.value = 'unit'
  newNodeName.value = ''
}

const cancelAddNode = () => {
  addNodeModalVisible.value = false
  addParentNode.value = null
  newNodeType.value = 'unit'
  newNodeName.value = ''
}

const confirmDelete = () => {
  if (!contextMenuNode.value) return
  
  const nodeTitle = contextMenuNode.value.title
  
  // 确认删除
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除节点"${nodeTitle}"吗？`,
    onOk() {
      emit('node-delete', contextMenuNode.value)
    }
  })
}

const copyNode = () => {
  copiedNode.value = { ...contextMenuNode.value }
  message.success('节点已复制')
}

const pasteNode = () => {
  if (!copiedNode.value || !contextMenuNode.value) return
  
  emit('node-paste', contextMenuNode.value, copiedNode.value)
  message.success('节点已粘贴')
}

const showProperties = () => {
  emit('node-properties', contextMenuNode.value)
}
</script>

<style lang="scss" scoped>
.project-structure-tree {
  height: 100%;
  
  :deep(.ant-tree) {
    background: transparent;
    
    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.ant-tree-node-selected {
        background-color: #e6f7ff;
      }
    }
  }
}

.tree-node-title {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
  
  .node-name {
    flex: 1;
    font-size: 13px;
  }
  
  .node-stats {
    font-size: 11px;
    color: #666;
  }
  
  .node-status {
    font-size: 12px;
  }
}

.node-icon {
  font-size: 14px;
}

// 节点状态样式
.node-editing {
  .node-name {
    color: #1890ff;
    border: 1px solid #1890ff;
    animation: blink 1s infinite;
  }
}

.node-locked {
  .node-name {
    color: #999;
  }
}

.node-error {
  .node-name {
    color: #ff4d4f;
  }
}

.node-completed {
  .node-name {
    color: #52c41a;
  }
}

@keyframes blink {
  0%, 50% { border-color: #1890ff; }
  51%, 100% { border-color: transparent; }
}
</style>
