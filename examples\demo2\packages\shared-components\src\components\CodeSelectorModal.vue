<!--
 * @Description: 编码选择器模态框组件
 * @Author: AI Assistant
 * @Date: 2025-08-28
 * @Features: 清单库选择、定额库选择、智能搜索、历史记录
-->
<template>
  <a-modal
    v-model:visible="visible"
    title="选择项目编码"
    width="1000px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="code-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-controls">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="输入编码或名称关键字搜索"
            style="width: 300px"
            @search="handleSearch"
            @change="handleSearchChange"
          />
          <a-select
            v-model:value="searchType"
            style="width: 120px"
            @change="handleTypeChange"
          >
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="list">清单库</a-select-option>
            <a-select-option value="quota">定额库</a-select-option>
          </a-select>
          <a-button @click="clearSearch">清空</a-button>
        </div>
        
        <!-- 快速筛选 -->
        <div class="quick-filters">
          <a-tag
            v-for="filter in quickFilters"
            :key="filter.key"
            :color="selectedFilter === filter.key ? 'blue' : 'default'"
            @click="selectQuickFilter(filter.key)"
            style="cursor: pointer"
          >
            {{ filter.label }}
          </a-tag>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 左侧分类树 -->
        <div class="category-tree">
          <a-tree
            :tree-data="categoryTree"
            :selected-keys="selectedCategoryKeys"
            :expanded-keys="expandedCategoryKeys"
            @select="handleCategorySelect"
            @expand="handleCategoryExpand"
          >
            <template #title="{ dataRef }">
              <span>{{ dataRef.title }} ({{ dataRef.count || 0 }})</span>
            </template>
          </a-tree>
        </div>

        <!-- 右侧项目列表 -->
        <div class="item-list">
          <a-table
            :columns="itemColumns"
            :data-source="filteredItems"
            :row-selection="itemRowSelection"
            :scroll="{ y: 400 }"
            size="small"
            :pagination="paginationConfig"
            @change="handleTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'code'">
                <span class="item-code">{{ record.code }}</span>
              </template>
              <template v-else-if="column.key === 'name'">
                <span class="item-name" :title="record.name">{{ record.name }}</span>
              </template>
              <template v-else-if="column.key === 'unitPrice'">
                <span class="unit-price">{{ formatCurrency(record.unitPrice) }}</span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-button type="link" size="small" @click="selectItem(record)">
                  选择
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div class="footer-actions">
        <div class="selected-info">
          <span v-if="selectedItemData">
            已选择：{{ selectedItemData.code }} - {{ selectedItemData.name }}
          </span>
        </div>
        <div class="action-buttons">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="confirmSelect" :disabled="!selectedItemData">
            确定
          </a-button>
        </div>
      </div>

      <!-- 历史记录 -->
      <div class="history-section" v-if="recentItems.length > 0">
        <h4>最近使用</h4>
        <div class="recent-items">
          <a-tag
            v-for="item in recentItems"
            :key="item.code"
            @click="selectItem(item)"
            style="cursor: pointer; margin-bottom: 4px"
          >
            {{ item.code }} - {{ item.name }}
          </a-tag>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentRecord: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'code-selected'])

// 响应式数据
const searchKeyword = ref('')
const searchType = ref('all')
const selectedFilter = ref('all')
const selectedCategoryKeys = ref([])
const expandedCategoryKeys = ref(['01'])
const selectedItemKeys = ref([])
const selectedItemData = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)

// 快速筛选选项
const quickFilters = ref([
  { key: 'all', label: '全部' },
  { key: 'earthwork', label: '土方工程' },
  { key: 'foundation', label: '基础工程' },
  { key: 'structure', label: '结构工程' },
  { key: 'decoration', label: '装饰工程' },
  { key: 'installation', label: '安装工程' }
])

// 分类树数据
const categoryTree = ref([
  {
    key: '01',
    title: '土石方工程',
    count: 156,
    children: [
      { key: '0101', title: '土方工程', count: 89 },
      { key: '0102', title: '石方工程', count: 67 }
    ]
  },
  {
    key: '02',
    title: '地基处理与边坡支护工程',
    count: 234,
    children: [
      { key: '0201', title: '地基处理', count: 123 },
      { key: '0202', title: '边坡支护', count: 111 }
    ]
  },
  {
    key: '03',
    title: '桩基工程',
    count: 178
  }
])

// 项目数据
const allItems = ref([
  {
    id: 1,
    code: '010101001001',
    name: '平整场地',
    unit: 'm²',
    unitPrice: 3.50,
    category: '0101',
    type: 'list'
  },
  {
    id: 2,
    code: '010102001001',
    name: '挖基础土方',
    unit: 'm³',
    unitPrice: 28.50,
    category: '0101',
    type: 'list'
  },
  {
    id: 3,
    code: 'A1-1',
    name: '人工挖土方',
    unit: 'm³',
    unitPrice: 45.60,
    category: '0101',
    type: 'quota'
  }
])

// 最近使用项目
const recentItems = ref([
  {
    code: '010101001001',
    name: '平整场地',
    unit: 'm²',
    unitPrice: 3.50
  }
])

// 表格列定义
const itemColumns = [
  {
    title: '编码',
    key: 'code',
    width: 120
  },
  {
    title: '项目名称',
    key: 'name',
    ellipsis: true
  },
  {
    title: '单位',
    key: 'unit',
    width: 60,
    align: 'center'
  },
  {
    title: '参考单价',
    key: 'unitPrice',
    width: 100,
    align: 'right'
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    align: 'center'
  }
]

// 行选择配置
const itemRowSelection = {
  type: 'radio',
  selectedRowKeys: selectedItemKeys,
  onChange: (keys, rows) => {
    selectedItemKeys.value = keys
    selectedItemData.value = rows[0] || null
  }
}

// 分页配置
const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: filteredItems.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条`
}))

// 过滤后的项目列表
const filteredItems = computed(() => {
  let items = allItems.value

  // 按类型筛选
  if (searchType.value !== 'all') {
    items = items.filter(item => item.type === searchType.value)
  }

  // 按分类筛选
  if (selectedCategoryKeys.value.length > 0) {
    const categoryKey = selectedCategoryKeys.value[0]
    items = items.filter(item => item.category.startsWith(categoryKey))
  }

  // 按关键字搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    items = items.filter(item =>
      item.code.toLowerCase().includes(keyword) ||
      item.name.toLowerCase().includes(keyword)
    )
  }

  // 按快速筛选
  if (selectedFilter.value !== 'all') {
    // 这里可以根据快速筛选条件进一步过滤
  }

  return items
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法定义
const handleSearch = (value) => {
  searchKeyword.value = value
  currentPage.value = 1
}

const handleSearchChange = (e) => {
  searchKeyword.value = e.target.value
}

const handleTypeChange = (value) => {
  searchType.value = value
  currentPage.value = 1
}

const clearSearch = () => {
  searchKeyword.value = ''
  searchType.value = 'all'
  selectedFilter.value = 'all'
  selectedCategoryKeys.value = []
  currentPage.value = 1
}

const selectQuickFilter = (filterKey) => {
  selectedFilter.value = filterKey
  currentPage.value = 1
}

const handleCategorySelect = (keys, { node }) => {
  selectedCategoryKeys.value = keys
  currentPage.value = 1
}

const handleCategoryExpand = (keys) => {
  expandedCategoryKeys.value = keys
}

const handleTableChange = (pagination) => {
  currentPage.value = pagination.current
  pageSize.value = pagination.pageSize
}

const selectItem = (item) => {
  selectedItemKeys.value = [item.id]
  selectedItemData.value = item
}

const confirmSelect = () => {
  if (!selectedItemData.value) {
    message.warning('请选择一个项目')
    return
  }

  // 添加到最近使用
  const recentItem = {
    code: selectedItemData.value.code,
    name: selectedItemData.value.name,
    unit: selectedItemData.value.unit,
    unitPrice: selectedItemData.value.unitPrice
  }

  // 去重并添加到最前面
  recentItems.value = [
    recentItem,
    ...recentItems.value.filter(item => item.code !== recentItem.code)
  ].slice(0, 10) // 只保留最近10个

  emit('code-selected', selectedItemData.value.code, selectedItemData.value)
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
  // 重置状态
  selectedItemKeys.value = []
  selectedItemData.value = null
  searchKeyword.value = ''
}

const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value || 0)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 模态框打开时的初始化逻辑
    if (props.currentRecord?.code) {
      searchKeyword.value = props.currentRecord.code
    }
  }
})

// 生命周期
onMounted(() => {
  // 初始化展开第一个分类
  expandedCategoryKeys.value = ['01']
})
</script>

<style lang="scss" scoped>
.code-selector {
  .search-area {
    margin-bottom: 16px;
    
    .search-controls {
      display: flex;
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .quick-filters {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
  
  .main-content {
    display: flex;
    height: 450px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    
    .category-tree {
      width: 250px;
      border-right: 1px solid #e8e8e8;
      padding: 8px;
      overflow-y: auto;
      background: #fafafa;
    }
    
    .item-list {
      flex: 1;
      padding: 8px;
      
      .item-code {
        font-family: monospace;
        color: #1890ff;
      }
      
      .item-name {
        color: #333;
      }
      
      .unit-price {
        font-weight: 600;
        color: #52c41a;
      }
    }
  }
  
  .footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;
    
    .selected-info {
      color: #666;
      font-size: 14px;
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .history-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;
    
    h4 {
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
    }
    
    .recent-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}
</style>
